"""
Email Monitor Service for continuous email fetching from Outlook

This service runs in the background and continuously monitors for new emails
from all active Outlook connections. It uses delta queries for efficient
incremental synchronization.
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from contextlib import asynccontextmanager
from bson import ObjectId

from app.services.outlook_service import OutlookService
from app.services.outlook_service_admin import AdminOutlookService
from app.services.outlook_database import OutlookDatabase
from app.core.database import get_database
from app.core.config import settings

logger = logging.getLogger(__name__)


class EmailMonitorService:
    """
    Background service for continuous email monitoring
    """

    def __init__(self, outlook_service: Union[OutlookService, AdminOutlookService]):
        self.outlook_service = outlook_service
        self.database_ops = None  # Will be initialized when needed
        self.is_running = False
        self.monitor_task: Optional[asyncio.Task] = None
        self.sync_interval = 60  # Check for new emails every 60 seconds
        self.error_retry_delay = 300  # Wait 5 minutes before retrying after error
        self.monitored_connections: Optional[List[str]] = None  # None = monitor all, List = specific connections
        
    async def start_monitoring(self, connection_ids: Optional[List[str]] = None):
        """
        Start the email monitoring service

        Args:
            connection_ids: Optional list of specific connection IDs to monitor.
                          If None, monitors all active connections.
        """
        if self.is_running:
            logger.warning("Email monitoring service is already running")
            return

        self.monitored_connections = connection_ids
        if connection_ids:
            logger.info(f"🚀 Starting email monitoring service for {len(connection_ids)} specific connections...")
        else:
            logger.info("🚀 Starting email monitoring service for all active connections...")

        self.is_running = True
        logger.info("📋 Creating monitoring task...")
        self.monitor_task = asyncio.create_task(self._monitor_loop())
        logger.info(f"📋 Task created: {self.monitor_task}")
        logger.info("✅ Email monitoring service started")
        
    async def stop_monitoring(self):
        """Stop the email monitoring service"""
        if not self.is_running:
            logger.warning("Email monitoring service is not running")
            return
            
        logger.info("Stopping email monitoring service...")
        self.is_running = False
        
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
                
        logger.info("✅ Email monitoring service stopped")
        
    async def _monitor_loop(self):
        """Main monitoring loop that runs continuously"""
        logger.info(f"🔄 Email monitoring loop started (interval: {self.sync_interval}s)")

        while self.is_running:
            try:
                logger.debug(f"🔍 Starting email sync cycle...")

                # Get all active connections
                active_connections = await self._get_active_connections()

                if not active_connections:
                    logger.debug("❌ No active connections found, waiting...")
                    await asyncio.sleep(self.sync_interval)
                    continue

                logger.debug(f"📊 Found {len(active_connections)} connections from admin collection")

                # Process each connection
                for i, connection in enumerate(active_connections, 1):
                    if not self.is_running:
                        break

                    connection_id = connection.get('_id', connection.get('connection_id', 'unknown'))
                    user_email = connection.get('user_email', 'unknown')
                    logger.debug(f"📧 Connection {i}: {user_email} (ID: {connection_id})")

                    try:
                        logger.debug(f"Syncing emails for connection: {connection_id}")
                        await self._sync_connection_emails(connection)
                    except Exception as e:
                        logger.error(f"Error syncing emails for connection {connection_id}: {str(e)}")
                        continue

                # Wait before next sync cycle
                if self.is_running:
                    logger.debug(f"Email sync cycle completed, waiting {self.sync_interval}s...")
                    await asyncio.sleep(self.sync_interval)
                    
            except Exception as e:
                logger.error(f"Error in email monitoring loop: {str(e)}")
                if self.is_running:
                    logger.info(f"Retrying in {self.error_retry_delay}s...")
                    await asyncio.sleep(self.error_retry_delay)
                    
    async def _get_active_connections(self) -> List[Dict[str, Any]]:
        """Get active Outlook connections (all or specific ones based on configuration)"""
        try:
            # Check if we're using AdminOutlookService or regular OutlookService
            if hasattr(self.outlook_service, 'database_ops'):
                # Using AdminOutlookService - get connections from admin collection
                connections = await self._get_admin_connections()
            else:
                # Using regular OutlookService - get connections from outlook_connections collection
                connections = await self._get_regular_connections()

            if self.monitored_connections:
                logger.debug(f"Found {len(connections)} active connections from specified list of {len(self.monitored_connections)}")
            else:
                logger.debug(f"Found {len(connections)} active connections (monitoring all)")

            return connections

        except Exception as e:
            logger.error(f"Error getting active connections: {str(e)}")
            return []

    async def _get_admin_connections(self) -> List[Dict[str, Any]]:
        """Get connections from admin collection"""
        try:
            # Get all admin documents with active connections
            db = await get_database()
            admin_collection = db.admin

            logger.debug("🔍 Fetching connections from admin collection...")

            # First, let's see what admin documents exist
            admin_count = await admin_collection.count_documents({})
            logger.debug(f"📊 Total admin documents: {admin_count}")

            # Check if any admin has outlook_connections
            admin_with_connections = await admin_collection.count_documents({
                "outlook_connections": {"$exists": True, "$ne": []}
            })
            logger.debug(f"📊 Admin documents with outlook_connections: {admin_with_connections}")

            # Find all admin documents that have active outlook connections
            pipeline = [
                {"$unwind": "$outlook_connections"},
                {"$match": {
                    "outlook_connections.is_active": True,
                    "outlook_connections.access_token_encrypted": {"$exists": True, "$ne": None}
                }},
                {"$project": {
                    "_id": {"$toString": "$outlook_connections.connection_id"},  # Convert ObjectId to string
                    "user_email": "$outlook_connections.user_email",
                    "access_token_encrypted": "$outlook_connections.access_token_encrypted",
                    "refresh_token_encrypted": "$outlook_connections.refresh_token_encrypted",
                    "token_expires_at": "$outlook_connections.token_expires_at",
                    "scopes": "$outlook_connections.scopes",
                    "is_active": "$outlook_connections.is_active",
                    "created_at": "$outlook_connections.created_at",
                    "updated_at": "$outlook_connections.updated_at",
                    "last_sync_at": "$outlook_connections.last_sync_at",
                    "delta_token": "$outlook_connections.delta_token"
                }}
            ]

            logger.debug(f"🔍 Running aggregation pipeline: {pipeline}")

            # If specific connections are configured, filter by them
            if self.monitored_connections:
                pipeline.append({
                    "$match": {
                        "_id": {"$in": self.monitored_connections}
                    }
                })
                logger.debug(f"🔍 Filtering for specific connections: {self.monitored_connections}")

            cursor = admin_collection.aggregate(pipeline)
            connections = await cursor.to_list(length=None)

            logger.debug(f"📊 Found {len(connections)} connections from admin collection")
            for i, conn in enumerate(connections):
                logger.debug(f"📧 Connection {i+1}: {conn.get('user_email', 'unknown')} (ID: {conn.get('_id', 'unknown')})")

            return connections

        except Exception as e:
            logger.error(f"Error getting admin connections: {str(e)}")
            return []

    async def _get_regular_connections(self) -> List[Dict[str, Any]]:
        """Get connections from regular outlook_connections collection"""
        try:
            db = await get_database()
            connections_collection = db.outlook_connections

            # Build query based on monitoring configuration
            query = {
                "is_active": True,
                "access_token_encrypted": {"$exists": True, "$ne": None}
            }

            # If specific connections are configured, filter by them
            if self.monitored_connections:
                from bson import ObjectId
                # Convert string IDs to ObjectIds for MongoDB query
                object_ids = []
                for conn_id in self.monitored_connections:
                    try:
                        object_ids.append(ObjectId(conn_id))
                    except Exception as e:
                        logger.warning(f"Invalid connection ID format: {conn_id} - {e}")
                        continue

                if object_ids:
                    query["_id"] = {"$in": object_ids}
                else:
                    logger.warning("No valid connection IDs provided, monitoring will be empty")
                    return []

            cursor = connections_collection.find(query)
            connections = await cursor.to_list(length=None)

            return connections

        except Exception as e:
            logger.error(f"Error getting regular connections: {str(e)}")
            return []
            
    async def _sync_connection_emails(self, connection: Dict[str, Any]):
        """Sync emails for a specific connection with respect to email fetch mode"""
        connection_id = str(connection["_id"])

        try:
            logger.debug(f"Syncing emails for connection: {connection_id}")

            # Get connection settings
            email_fetch_mode = connection.get("email_fetch_mode", "new_only")
            initial_sync_completed = connection.get("initial_sync_completed", False)
            created_at = connection.get("created_at")

            logger.debug(f"Connection {connection_id}: email_fetch_mode={email_fetch_mode}, initial_sync_completed={initial_sync_completed}")

            # Get the last delta link for this connection
            delta_link = await self._get_last_delta_link(connection_id)

            # For now, use regular email fetch (delta queries can be implemented later)
            # This is a simplified version that will work with the existing API
            from app.schemas.outlook import EmailFetchRequest

            # Determine if we need to apply date filtering for "new_only" mode
            date_filter = None
            if email_fetch_mode == "new_only" and not initial_sync_completed and created_at:
                # For "new_only" mode on first sync, only fetch emails received after connection creation
                date_filter = created_at.isoformat() + "Z" if hasattr(created_at, 'isoformat') else str(created_at)
                logger.info(f"Applying date filter for new_only mode: receivedDateTime >= {date_filter}")

            request = EmailFetchRequest(
                user_email=connection.get("user_email", ""),
                limit=settings.EMAIL_FETCH_BATCH_SIZE,
                skip=0,
                folder="inbox",
                date_filter=date_filter  # This will be used by the email service
            )

            result = await self.outlook_service.fetch_emails(
                connection_id=connection_id,
                request=request
            )

            if result.success:
                new_emails_count = result.new_emails
                if new_emails_count > 0:
                    logger.info(f"✅ Found {new_emails_count} new emails for connection {connection_id}")
                else:
                    logger.debug(f"No new emails for connection {connection_id}")

                # Mark initial sync as completed if this was the first sync
                if not initial_sync_completed:
                    await self._mark_initial_sync_completed(connection_id)
                    logger.info(f"Marked initial sync as completed for connection {connection_id}")
            else:
                logger.warning(f"Failed to sync emails for connection {connection_id}: {result.message}")

        except Exception as e:
            logger.error(f"Error syncing connection {connection_id}: {str(e)}")

    async def _mark_initial_sync_completed(self, connection_id: str):
        """Mark the initial sync as completed for a connection"""
        try:
            db = await get_database()
            admin_collection = db.admin

            # Update the connection to mark initial sync as completed
            await admin_collection.update_one(
                {"connections.connection_id": ObjectId(connection_id)},
                {"$set": {"connections.$.initial_sync_completed": True}}
            )
            logger.debug(f"Marked initial sync completed for connection {connection_id}")

        except Exception as e:
            logger.error(f"Error marking initial sync completed for connection {connection_id}: {str(e)}")
            
    async def _get_last_delta_link(self, connection_id: str) -> Optional[str]:
        """Get the last stored delta link for a connection"""
        try:
            db = await get_database()
            delta_collection = db.email_delta_links
            
            result = await delta_collection.find_one({
                "connection_id": connection_id
            })
            
            return result.get("delta_link") if result else None
            
        except Exception as e:
            logger.error(f"Error getting delta link for {connection_id}: {str(e)}")
            return None
            
    async def _store_delta_link(self, connection_id: str, delta_link: str):
        """Store the delta link for a connection"""
        try:
            db = await get_database()
            delta_collection = db.email_delta_links
            
            await delta_collection.update_one(
                {"connection_id": connection_id},
                {
                    "$set": {
                        "connection_id": connection_id,
                        "delta_link": delta_link,
                        "updated_at": datetime.utcnow()
                    }
                },
                upsert=True
            )
            
        except Exception as e:
            logger.error(f"Error storing delta link for {connection_id}: {str(e)}")
            
    async def get_monitoring_status(self) -> Dict[str, Any]:
        """Get the current status of the monitoring service"""
        status = {
            "is_running": self.is_running,
            "sync_interval": self.sync_interval,
            "error_retry_delay": self.error_retry_delay,
            "task_status": "running" if self.monitor_task and not self.monitor_task.done() else "stopped",
            "monitoring_mode": "specific_connections" if self.monitored_connections else "all_connections",
            "monitored_connections": self.monitored_connections,
            "monitored_connection_count": len(self.monitored_connections) if self.monitored_connections else None
        }

        # Get current active connections count
        try:
            active_connections = await self._get_active_connections()
            status["active_connections_found"] = len(active_connections)
        except Exception as e:
            logger.error(f"Error getting active connections count: {e}")
            status["active_connections_found"] = "error"

        return status
        
    def set_sync_interval(self, interval_seconds: int):
        """Set the sync interval (minimum 30 seconds)"""
        if interval_seconds < 30:
            raise ValueError("Sync interval must be at least 30 seconds")
        self.sync_interval = interval_seconds
        logger.info(f"Sync interval updated to {interval_seconds}s")

    def set_monitored_connections(self, connection_ids: Optional[List[str]] = None):
        """
        Update the list of monitored connections

        Args:
            connection_ids: List of connection IDs to monitor, or None to monitor all
        """
        self.monitored_connections = connection_ids
        if connection_ids:
            logger.info(f"Updated monitored connections to: {connection_ids}")
        else:
            logger.info("Updated monitoring to include all active connections")

    def add_monitored_connection(self, connection_id: str):
        """
        Add a connection to the monitored list

        Args:
            connection_id: Connection ID to add to monitoring
        """
        if self.monitored_connections is None:
            self.monitored_connections = []

        if connection_id not in self.monitored_connections:
            self.monitored_connections.append(connection_id)
            logger.info(f"Added connection {connection_id} to monitoring list")
        else:
            logger.info(f"Connection {connection_id} is already being monitored")

    def remove_monitored_connection(self, connection_id: str):
        """
        Remove a connection from the monitored list

        Args:
            connection_id: Connection ID to remove from monitoring
        """
        if self.monitored_connections and connection_id in self.monitored_connections:
            self.monitored_connections.remove(connection_id)
            logger.info(f"Removed connection {connection_id} from monitoring list")
        else:
            logger.info(f"Connection {connection_id} was not in the monitoring list")


# Global instance
_email_monitor_service: Optional[EmailMonitorService] = None


def get_email_monitor_service(outlook_service: Union[OutlookService, AdminOutlookService]) -> EmailMonitorService:
    """Get or create the global email monitor service instance"""
    global _email_monitor_service
    if _email_monitor_service is None:
        logger.info("Creating new EmailMonitorService instance")
        _email_monitor_service = EmailMonitorService(outlook_service)
    else:
        # Update the outlook service reference in case it's a new instance
        _email_monitor_service.outlook_service = outlook_service
        logger.debug(f"Reusing existing EmailMonitorService instance (is_running: {_email_monitor_service.is_running})")
    return _email_monitor_service


@asynccontextmanager
async def email_monitor_lifespan(outlook_service: Union[OutlookService, AdminOutlookService]):
    """Context manager for email monitor service lifecycle"""
    monitor_service = get_email_monitor_service(outlook_service)
    
    try:
        await monitor_service.start_monitoring()
        yield monitor_service
    finally:
        await monitor_service.stop_monitoring()
