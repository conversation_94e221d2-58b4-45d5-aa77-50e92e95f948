"""
Outlook-related Pydantic schemas for Microsoft Graph API integration
"""

from typing import Optional, List, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field
from bson import ObjectId
from app.schemas.bill import PyObjectId


class OutlookAuthUrlRequest(BaseModel):
    """Schema for authorization URL request"""
    redirect_uri: Optional[str] = Field(None, description="Custom redirect URI")
    state: Optional[str] = Field(None, description="Custom state parameter")


class OutlookAuthCallbackRequest(BaseModel):
    """Schema for OAuth callback request"""
    code: str = Field(..., description="Authorization code from Microsoft")
    state: Optional[str] = Field(None, description="State parameter for verification")


class OutlookConnectionCreate(BaseModel):
    """Schema for creating Outlook connection"""
    user_email: str = Field(..., description="User's email address")
    tenant_id: Optional[str] = Field(None, description="Azure AD tenant ID")
    access_token: Optional[str] = Field(None, description="Access token")
    refresh_token: Optional[str] = Field(None, description="Refresh token")
    token_expires_at: Optional[datetime] = Field(None, description="Token expiration time")
    scopes: Optional[List[str]] = Field(None, description="OAuth scopes")
    email_fetch_mode: Optional[str] = Field("new_only", description="Email fetching mode: 'new_only' or 'all_emails'")


class OutlookConnectionUpdate(BaseModel):
    """Schema for updating Outlook connection"""
    access_token: Optional[str] = Field(None, description="New access token")
    refresh_token: Optional[str] = Field(None, description="New refresh token")
    token_expires_at: Optional[datetime] = Field(None, description="New token expiration time")
    scopes: Optional[List[str]] = Field(None, description="OAuth scopes")
    is_active: Optional[bool] = Field(None, description="Connection active status")
    last_sync_at: Optional[datetime] = Field(None, description="Last sync timestamp")
    delta_token: Optional[str] = Field(None, description="Delta token for incremental sync")


class TokenInfo(BaseModel):
    """Schema for token information"""
    access_token: str
    refresh_token: str
    expires_at: datetime
    token_type: str = "Bearer"
    scope: Optional[str] = None


class TokenRefreshResult(BaseModel):
    """Schema for token refresh result"""
    success: bool
    message: str
    new_access_token: Optional[str] = None
    new_refresh_token: Optional[str] = None
    new_expires_at: Optional[datetime] = None
    error_code: Optional[str] = None


class OutlookConnectionInDB(BaseModel):
    """Schema for Outlook connection stored in database"""
    id: Optional[PyObjectId] = Field(default_factory=PyObjectId, alias="_id")
    user_email: str
    tenant_id: Optional[str] = None
    access_token_encrypted: str
    refresh_token_encrypted: str
    token_expires_at: datetime
    scopes: Optional[List[str]] = Field(None, description="OAuth scopes")
    is_active: bool = True
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    last_sync_at: Optional[datetime] = None
    delta_token: Optional[str] = None
    email_fetch_mode: str = Field(default="new_only", description="Email fetching mode: 'new_only' or 'all_emails'")
    initial_sync_completed: bool = Field(default=False, description="Whether the initial email sync has been completed")
    
    model_config = {
        "populate_by_name": True,
        "arbitrary_types_allowed": True,
        "json_encoders": {ObjectId: str}
    }


class OutlookConnectionResponse(BaseModel):
    """Schema for Outlook connection response"""
    success: bool
    message: str
    connection_id: Optional[str] = None
    user_email: Optional[str] = None
    is_active: Optional[bool] = None
    created_at: Optional[datetime] = None
    last_sync_at: Optional[datetime] = None


class OutlookAuthUrlResponse(BaseModel):
    """Schema for OAuth authorization URL response"""
    success: bool
    auth_url: str
    state: str
    message: str


class OutlookTokenResponse(BaseModel):
    """Schema for OAuth token response"""
    success: bool
    message: str
    connection_id: Optional[str] = None
    user_email: Optional[str] = None


class EmailAttachment(BaseModel):
    """Schema for email attachment"""
    id: str
    name: str
    content_type: str
    size: int
    is_inline: bool = False


class EmailAddress(BaseModel):
    """Schema for email address"""
    name: Optional[str] = None
    address: str


class OutlookEmail(BaseModel):
    """Schema for Outlook email"""
    id: str
    internet_message_id: str
    subject: Optional[str] = None
    body_preview: Optional[str] = None
    body_content: Optional[str] = None
    body_content_type: str = "text"
    from_address: Optional[EmailAddress] = None
    to_recipients: List[EmailAddress] = []
    cc_recipients: List[EmailAddress] = []
    bcc_recipients: List[EmailAddress] = []
    received_datetime: datetime
    sent_datetime: Optional[datetime] = None
    has_attachments: bool = False
    attachments: List[EmailAttachment] = []
    importance: str = "normal"
    is_read: bool = False
    web_link: Optional[str] = None


class OutlookEmailInDB(BaseModel):
    """Schema for Outlook email stored in database"""
    id: Optional[PyObjectId] = Field(default_factory=PyObjectId, alias="_id")
    connection_id: str
    outlook_email_id: str
    internet_message_id: str
    subject: Optional[str] = None
    body_preview: Optional[str] = None
    body_content: Optional[str] = None
    body_content_type: str = "text"
    from_address: Optional[Dict[str, Any]] = None
    to_recipients: List[Dict[str, Any]] = []
    cc_recipients: List[Dict[str, Any]] = []
    bcc_recipients: List[Dict[str, Any]] = []
    received_datetime: datetime
    sent_datetime: Optional[datetime] = None
    has_attachments: bool = False
    attachments: List[Dict[str, Any]] = []
    importance: str = "normal"
    is_read: bool = False
    web_link: Optional[str] = None
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    
    model_config = {
        "populate_by_name": True,
        "arbitrary_types_allowed": True,
        "json_encoders": {ObjectId: str}
    }


class EmailFetchRequest(BaseModel):
    """Schema for email fetch request"""
    user_email: str = Field(..., description="User's email address")
    limit: Optional[int] = Field(50, ge=1, le=1000, description="Maximum number of emails to fetch")
    skip: Optional[int] = Field(0, ge=0, description="Number of emails to skip for pagination")
    include_attachments: bool = Field(False, description="Whether to include attachment details")
    folder: Optional[str] = Field("inbox", description="Folder to fetch emails from")
    filter_unread: Optional[bool] = Field(None, description="Filter for unread emails only")
    filter_has_attachments: Optional[bool] = Field(None, description="Filter for emails with attachments")
    date_filter: Optional[str] = Field(None, description="ISO datetime string to filter emails from (for new_only mode)")
    # Removed date filtering - system now fetches all emails regardless of date
    # date_from: Optional[datetime] = Field(None, description="Filter emails from this date")
    # date_to: Optional[datetime] = Field(None, description="Filter emails to this date")


class EmailFetchResponse(BaseModel):
    """Schema for email fetch response"""
    success: bool
    message: str
    total_emails: int
    new_emails: int
    emails: List[OutlookEmail] = []
    has_more: bool = False
    next_page_token: Optional[str] = None


class OutlookErrorResponse(BaseModel):
    """Schema for Outlook API error responses"""
    success: bool = False
    error_code: str
    error_message: str
    details: Optional[Dict[str, Any]] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    correlation_id: Optional[str] = None


class OutlookConnectionStatus(BaseModel):
    """Schema for Outlook connection status"""
    success: bool
    message: str
    is_connected: bool
    user_email: Optional[str] = None
    connection_id: Optional[str] = None
    last_sync_at: Optional[datetime] = None
    token_expires_at: Optional[datetime] = None
    is_token_valid: bool = False


class OutlookSyncStatus(BaseModel):
    """Schema for Outlook sync status"""
    success: bool
    message: str
    connection_id: str
    user_email: str
    last_sync_at: Optional[datetime] = None
    emails_synced: int = 0
    sync_duration_seconds: Optional[float] = None


class OutlookConnectionDetail(BaseModel):
    """Schema for detailed Outlook connection information"""
    connection_id: str = Field(..., description="Connection ID")
    user_email: str = Field(..., description="User email address")
    status: str = Field(..., description="Connection status (Connected/Disconnected/Active)")
    is_active: bool = Field(..., description="Whether connection is active")
    is_token_valid: bool = Field(..., description="Whether token is valid")
    created_at: datetime = Field(..., description="Connection creation timestamp")
    last_sync_at: Optional[datetime] = Field(None, description="Last sync timestamp")
    token_expires_at: Optional[datetime] = Field(None, description="Token expiration timestamp")
    email_count: int = Field(default=0, description="Total number of emails")
    sync_interval_seconds: Optional[int] = Field(None, description="Sync interval in seconds")
    scopes: Optional[List[str]] = Field(None, description="OAuth scopes")
    error_message: Optional[str] = Field(None, description="Error message if any")
    health_status: str = Field(default="unknown", description="Connection health status")


class OutlookConnectionDetailResponse(BaseModel):
    """Schema for detailed Outlook connections response"""
    success: bool = Field(..., description="Whether request was successful")
    message: str = Field(..., description="Response message")
    admin_email: str = Field(..., description="Admin email address")
    connections: List[OutlookConnectionDetail] = Field(..., description="List of detailed connection information")
    total_connections: int = Field(..., description="Total number of connections")
    active_connections: int = Field(..., description="Number of active connections")
    sync_interval_seconds: Optional[int] = Field(None, description="Global sync interval in seconds")


class EmailMetadata(BaseModel):
    """Schema for email metadata used in tracking"""
    message_id: str
    internet_message_id: str
    subject: Optional[str] = None
    sender_email: Optional[str] = None
    sender_name: Optional[str] = None
    received_datetime: datetime
    has_attachments: bool = False
    importance: str = "normal"
    is_read: bool = False
    folder_name: str = "inbox"


class EmailContent(BaseModel):
    """Schema for email content with body and attachments"""
    subject: Optional[str] = None
    body: Optional[str] = None
    sender_email: Optional[str] = None
    sender_name: Optional[str] = None
    received_datetime: datetime
    attachments: List[Dict[str, Any]] = []


class EmailAttachmentInDB(BaseModel):
    """Schema for email attachment stored in database"""
    name: str
    content_type: str
    size: int
    file_path: str  # Local file path where attachment is stored
    download_url: Optional[str] = None  # Original Graph API URL (for reference)
    downloaded_at: datetime


class MonitoringStartRequest(BaseModel):
    """Schema for starting email monitoring"""
    connection_ids: Optional[List[str]] = Field(None, description="List of connection IDs to monitor. If empty, monitors all active connections.")


class MonitoringConnectionRequest(BaseModel):
    """Schema for adding/removing connections from monitoring"""
    connection_id: str = Field(..., description="Connection ID to add or remove from monitoring")


class MonitoringSetConnectionsRequest(BaseModel):
    """Schema for setting monitored connections"""
    connection_ids: Optional[List[str]] = Field(None, description="List of connection IDs to monitor. If empty, monitors all active connections.")


class MonitoringStatusResponse(BaseModel):
    """Schema for monitoring status response"""
    success: bool
    is_running: bool
    sync_interval: int
    error_retry_delay: int
    task_status: str
    monitoring_mode: str
    monitored_connections: Optional[List[str]]
    monitored_connection_count: Optional[int]
    active_connections_found: int


class EmailTrackingInDB(BaseModel):
    """Schema for email tracking stored in database with full content"""
    id: Optional[PyObjectId] = Field(default_factory=PyObjectId, alias="_id")
    connection_id: PyObjectId
    message_id: str
    internet_message_id: str
    subject: Optional[str] = None
    sender_email: Optional[str] = None
    sender_name: Optional[str] = None
    received_datetime: datetime
    has_attachments: bool = False
    importance: str = "normal"
    is_read: bool = False
    folder_name: str = "inbox"

    # Enhanced content fields
    body_content: Optional[str] = None  # Email body content
    body_content_type: str = "text"  # "text" or "html"
    body_preview: Optional[str] = None  # Short preview of the email

    # Attachment information
    attachments: List[EmailAttachmentInDB] = []  # List of downloaded attachments
    attachment_count: int = 0  # Number of attachments

    # Processing information
    processing_status: str = "processed"
    processed_at: Optional[datetime] = None
    content_downloaded: bool = False  # Whether full content was downloaded
    attachments_downloaded: bool = False  # Whether attachments were downloaded

    # Timestamps
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

    model_config = {
        "populate_by_name": True,
        "arbitrary_types_allowed": True,
        "json_encoders": {ObjectId: str}
    }


class EmailListResponse(BaseModel):
    """Schema for email list response"""
    success: bool
    message: str
    total_emails: int
    emails: List[OutlookEmail] = []
    has_more: bool = False
    next_page_token: Optional[str] = None