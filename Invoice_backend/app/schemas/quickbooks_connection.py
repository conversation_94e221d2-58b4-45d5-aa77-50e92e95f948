"""
QuickBooks Connection-related Pydantic schemas
"""

from typing import Optional, Dict, Any
from datetime import datetime, timezone, UTC
from pydantic import BaseModel, Field, field_validator, model_validator
from bson import ObjectId
from app.schemas.bill import PyObjectId


class QBConnectionCreate(BaseModel):
    """Schema for creating a new QuickBooks connection"""
    realm_id: str = Field(..., min_length=1, description="QuickBooks Company ID (Realm ID)")
    client_id: str = Field(..., min_length=1, description="QuickBooks App Client ID")
    client_secret: str = Field(..., min_length=1, description="QuickBooks App Client Secret")
    access_token: str = Field(..., min_length=1, description="OAuth Access Token")
    refresh_token: str = Field(..., min_length=1, description="OAuth Refresh Token")
    token_expires_at: datetime = Field(..., description="Access token expiration timestamp")
    environment: str = Field(default="sandbox", description="QuickBooks environment (sandbox or production)")
    
    @field_validator('realm_id', 'client_id', 'client_secret', 'access_token', 'refresh_token')
    @classmethod
    def validate_required_fields(cls, v):
        if not v or not v.strip():
            raise ValueError('Field cannot be empty')
        return v.strip()
    
    @field_validator('environment')
    @classmethod
    def validate_environment(cls, v):
        if v not in ['sandbox', 'production']:
            raise ValueError('Environment must be either "sandbox" or "production"')
        return v
    
    @field_validator('token_expires_at')
    @classmethod
    def validate_token_expiration(cls, v):
        if v <= datetime.now(UTC):
            raise ValueError('Token expiration must be in the future')
        return v


class QBConnectionUpdate(BaseModel):
    """Schema for updating an existing QuickBooks connection"""
    realm_id: Optional[str] = Field(None, min_length=1, description="QuickBooks Company ID (Realm ID)")
    client_id: Optional[str] = Field(None, min_length=1, description="QuickBooks App Client ID")
    client_secret: Optional[str] = Field(None, min_length=1, description="QuickBooks App Client Secret")
    access_token: Optional[str] = Field(None, min_length=1, description="OAuth Access Token")
    refresh_token: Optional[str] = Field(None, min_length=1, description="OAuth Refresh Token")
    token_expires_at: Optional[datetime] = Field(None, description="Access token expiration timestamp")
    environment: Optional[str] = Field(None, description="QuickBooks environment (sandbox or production)")
    
    @field_validator('realm_id', 'client_id', 'client_secret', 'access_token', 'refresh_token')
    @classmethod
    def validate_optional_fields(cls, v):
        if v is not None and (not v or not v.strip()):
            raise ValueError('Field cannot be empty if provided')
        return v.strip() if v else v
    
    @field_validator('environment')
    @classmethod
    def validate_environment(cls, v):
        if v is not None and v not in ['sandbox', 'production']:
            raise ValueError('Environment must be either "sandbox" or "production"')
        return v
    
    @field_validator('token_expires_at')
    @classmethod
    def validate_token_expiration(cls, v):
        if v is not None:
            # Ensure both datetimes are timezone-aware for comparison
            now = datetime.now(timezone.utc)
            if v.tzinfo is None:
                # If v is naive, assume it's UTC
                v = v.replace(tzinfo=timezone.utc)
            if v <= now:
                raise ValueError('Token expiration must be in the future')
        return v
    
    @model_validator(mode='after')
    def validate_at_least_one_field(self):
        """Ensure at least one field is provided for update"""
        fields = [self.realm_id, self.client_id, self.client_secret, 
                 self.access_token, self.refresh_token, self.token_expires_at, self.environment]
        if all(field is None for field in fields):
            raise ValueError('At least one field must be provided for update')
        return self


class QBConnectionInDB(BaseModel):
    """Schema for QuickBooks connection stored in database"""
    id: Optional[PyObjectId] = Field(default_factory=PyObjectId, alias="_id")
    realm_id: str
    client_id: str
    client_secret_encrypted: str  # Encrypted client secret
    access_token_encrypted: str   # Encrypted access token
    refresh_token_encrypted: str  # Encrypted refresh token
    token_expires_at: datetime
    environment: str = "sandbox"
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    last_tested: Optional[datetime] = None
    is_active: bool = True
    company_info: Optional[Dict[str, Any]] = None  # Store QB company information
    
    model_config = {
        "populate_by_name": True,
        "arbitrary_types_allowed": True,
        "json_encoders": {ObjectId: str}
    }


class QBConnectionStatus(BaseModel):
    """Schema for QuickBooks connection status response"""
    is_connected: bool = Field(..., description="Whether a connection is established")
    realm_id: Optional[str] = Field(None, description="QuickBooks Company ID")
    environment: Optional[str] = Field(None, description="QuickBooks environment")
    token_expires_at: Optional[datetime] = Field(None, description="Token expiration timestamp")
    token_status: str = Field(..., description="Token status: valid, expired, near_expiry")
    last_tested: Optional[datetime] = Field(None, description="Last connection test timestamp")
    company_name: Optional[str] = Field(None, description="QuickBooks company name")
    company_id: Optional[str] = Field(None, description="QuickBooks company ID")
    
    @field_validator('token_status')
    @classmethod
    def validate_token_status(cls, v):
        valid_statuses = ['valid', 'expired', 'near_expiry', 'unknown']
        if v not in valid_statuses:
            raise ValueError(f'Token status must be one of: {", ".join(valid_statuses)}')
        return v


class QBConnectionResponse(BaseModel):
    """Schema for QuickBooks connection operation response"""
    success: bool = Field(..., description="Whether the operation was successful")
    message: str = Field(..., description="Response message")
    connection_status: Optional[QBConnectionStatus] = Field(None, description="Connection status details")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Response timestamp")


class QBTestResult(BaseModel):
    """Schema for QuickBooks connection test result"""
    success: bool = Field(..., description="Whether the test was successful")
    message: str = Field(..., description="Test result message")
    company_info: Optional[Dict[str, Any]] = Field(None, description="QuickBooks company information")
    response_time_ms: Optional[int] = Field(None, description="API response time in milliseconds")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Test timestamp")


class QBCredentials(BaseModel):
    """Schema for QuickBooks credentials (decrypted, for internal use)"""
    realm_id: str
    client_id: str
    client_secret: str
    access_token: str
    refresh_token: str
    token_expires_at: datetime
    environment: str = "sandbox"
    
    model_config = {
        "str_strip_whitespace": True
    }


class TokenInfo(BaseModel):
    """Schema for token information and validation"""
    access_token: str
    refresh_token: str
    expires_at: datetime
    is_expired: bool = Field(default=False, description="Whether the token is expired")
    is_near_expiry: bool = Field(default=False, description="Whether the token is near expiry")
    minutes_until_expiry: Optional[int] = Field(None, description="Minutes until token expires")
    
    @model_validator(mode='after')
    def calculate_expiry_status(self):
        """Calculate token expiry status based on current time"""
        now = datetime.utcnow()
        self.is_expired = self.expires_at <= now
        
        # Calculate minutes until expiry
        time_diff = self.expires_at - now
        self.minutes_until_expiry = int(time_diff.total_seconds() / 60) if time_diff.total_seconds() > 0 else 0
        
        # Consider token near expiry if less than 10 minutes remaining
        self.is_near_expiry = self.minutes_until_expiry <= 10 and not self.is_expired
        
        return self


class TokenRefreshResult(BaseModel):
    """Schema for token refresh operation result"""
    success: bool = Field(..., description="Whether the refresh was successful")
    message: str = Field(..., description="Refresh result message")
    new_access_token: Optional[str] = Field(None, description="New access token (if successful)")
    new_refresh_token: Optional[str] = Field(None, description="New refresh token (if provided)")
    new_expires_at: Optional[datetime] = Field(None, description="New token expiration (if successful)")
    error_code: Optional[str] = Field(None, description="Error code (if failed)")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Refresh timestamp")


class QBErrorResponse(BaseModel):
    """Schema for QuickBooks API error responses"""
    success: bool = Field(default=False, description="Always false for error responses")
    error_code: str = Field(..., description="Error code identifier")
    error_message: str = Field(..., description="Human-readable error message")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Error timestamp")
    
    @field_validator('error_code')
    @classmethod
    def validate_error_code(cls, v):
        if not v or not v.strip():
            raise ValueError('Error code cannot be empty')
        return v.strip().upper()


class QBCompanyInfo(BaseModel):
    """Schema for QuickBooks company information"""
    company_name: str = Field(..., description="Company name from QuickBooks")
    company_id: str = Field(..., description="Company ID from QuickBooks")
    country: Optional[str] = Field(None, description="Company country")
    created_time: Optional[datetime] = Field(None, description="Company creation time in QB")
    last_updated_time: Optional[datetime] = Field(None, description="Last update time in QB")
    fiscal_year_start_month: Optional[str] = Field(None, description="Fiscal year start month")
    supported_languages: Optional[str] = Field(None, description="Supported languages")