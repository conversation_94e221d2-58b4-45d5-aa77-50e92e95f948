"""
Outlook integration endpoints for Microsoft Graph API (Admin Protected)
"""

import logging
import webbrowser
from typing import Optional, Dict, Any
from fastapi import APIRouter, HTTPException, Depends, Query, Request
from fastapi.encoders import jsonable_encoder
from datetime import datetime

from app.schemas.outlook import (
    OutlookAuthUrlRequest, OutlookAuthUrlResponse,
    OutlookConnectionResponse, EmailFetchRequest, EmailFetchResponse,
    OutlookConnectionStatus, MonitoringStartRequest,
    MonitoringConnectionRequest, MonitoringSetConnectionsRequest, MonitoringStatusResponse,
    OutlookConnectionDetailResponse
)
from app.services.outlook_service_admin import AdminOutlookService, get_admin_outlook_service
from app.services.email_monitor_service import get_email_monitor_service
from app.services.microsoft_graph_client import MicrosoftGraphClient
from app.core.auth import get_current_admin

logger = logging.getLogger(__name__)
router = APIRouter()


# Remove the old get_outlook_service function since we're using the admin service now


@router.get("/auth/url", response_model=OutlookAuthUrlResponse)
async def get_authorization_url(
    open_browser: bool = Query(True, description="Automatically open the auth URL in browser"),
    email_fetch_mode: str = Query("new_only", description="Email fetching mode: 'new_only' (fetch emails after connection) or 'all_emails' (fetch all existing emails)"),
    current_admin: Dict[str, Any] = Depends(get_current_admin),
    outlook_service: AdminOutlookService = Depends(get_admin_outlook_service)
):
    """
    Get Microsoft OAuth authorization URL for connecting Outlook (Admin Only).

    This endpoint generates an authorization URL that the admin should visit to grant
    access to an Outlook account. Automatically opens the URL in the browser by default.
    After authentication and authorization, the connection will be associated with the admin.

    Args:
        open_browser: Whether to automatically open the URL in browser (defaults to true)
        email_fetch_mode: Email fetching behavior - 'new_only' to fetch only emails received after connection, 'all_emails' to fetch all existing emails
        current_admin: Current authenticated admin

    Returns:
        Authorization URL and state parameter
    """
    try:
        # Validate email_fetch_mode parameter
        if email_fetch_mode not in ["new_only", "all_emails"]:
            raise HTTPException(
                status_code=400,
                detail={
                    "success": False,
                    "error": "invalid_parameter",
                    "message": "email_fetch_mode must be either 'new_only' or 'all_emails'"
                }
            )

        admin_email = current_admin["admin_email"]
        logger.info(f"Generating Outlook authorization URL for admin: {admin_email} with email_fetch_mode: {email_fetch_mode}")

        result = await outlook_service.get_authorization_url(admin_email, email_fetch_mode)

        # Automatically open the URL in browser by default
        if open_browser and result.success:
            try:
                webbrowser.open(result.auth_url)
                logger.info("Opened authorization URL in browser")
                result.message = f"{result.message} (URL opened in browser)"
            except Exception as e:
                logger.warning(f"Failed to open browser: {str(e)}")

        return result

    except Exception as e:
        logger.error(f"Error generating authorization URL: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "success": False,
                "error": "authorization_url_failed",
                "message": f"Failed to generate authorization URL: {str(e)}"
            }
        )


@router.post("/auth/url", response_model=OutlookAuthUrlResponse)
async def create_authorization_url(
    open_browser: bool = Query(True, description="Automatically open the auth URL in browser"),
    email_fetch_mode: str = Query("new_only", description="Email fetching mode: 'new_only' (fetch emails after connection) or 'all_emails' (fetch all existing emails)"),
    current_admin: Dict[str, Any] = Depends(get_current_admin),
    outlook_service: AdminOutlookService = Depends(get_admin_outlook_service)
):
    """
    Create Microsoft OAuth authorization URL for connecting Outlook (Admin Only).

    This endpoint generates an authorization URL that the admin should visit to grant
    access to an Outlook account. Automatically opens the URL in the browser by default.
    After authentication and authorization, the connection will be associated with the admin.

    Args:
        open_browser: Whether to automatically open the URL in browser (defaults to true)
        email_fetch_mode: Email fetching behavior - 'new_only' to fetch only emails received after connection, 'all_emails' to fetch all existing emails
        current_admin: Current authenticated admin

    Returns:
        Authorization URL and state parameter
    """
    # Same implementation as GET endpoint
    return await get_authorization_url(open_browser, email_fetch_mode, current_admin, outlook_service)


@router.get("/auth/callback", response_model=OutlookConnectionResponse)
async def handle_oauth_callback(
    code: str = Query(..., description="Authorization code from Microsoft"),
    state: Optional[str] = Query(None, description="State parameter for verification"),
    outlook_service: AdminOutlookService = Depends(get_admin_outlook_service),
    request: Request = None
):
    """
    Handle OAuth callback from Microsoft and establish Outlook connection (Admin Only).

    This endpoint is called by Microsoft after the admin grants access.
    It exchanges the authorization code for access tokens and creates
    a new Outlook connection associated with the admin.

    Args:
        code: Authorization code from Microsoft OAuth
        state: State parameter for verification

    Returns:
        Connection response with success status and connection details
    """
    try:
        logger.info("Handling OAuth callback")

        # Parse state to get admin email
        if not state:
            return OutlookConnectionResponse(
                success=False,
                message="Missing state parameter"
            )

        try:
            import json
            state_data = json.loads(state)
            admin_email = state_data.get("admin_email")
            if not admin_email:
                return OutlookConnectionResponse(
                    success=False,
                    message="Invalid state parameter: missing admin_email"
                )
        except (json.JSONDecodeError, KeyError) as e:
            return OutlookConnectionResponse(
                success=False,
                message=f"Invalid state parameter: {str(e)}"
            )

        result = await outlook_service.handle_oauth_callback(admin_email, code, state)

        if not result.success:
            logger.error(f"OAuth callback failed: {result.message}")
            return result

        logger.info(f"OAuth callback completed successfully for: {result.user_email}")
        return result

    except Exception as e:
        logger.error(f"Error in OAuth callback: {str(e)}")
        return OutlookConnectionResponse(
            success=False,
            message=f"Authentication failed: {str(e)}"
        )


@router.get("/callback", response_model=OutlookConnectionResponse)
async def handle_oauth_callback_legacy(
    code: str = Query(..., description="Authorization code from Microsoft"),
    state: Optional[str] = Query(None, description="State parameter for verification"),
    outlook_service: AdminOutlookService = Depends(get_admin_outlook_service),
    request: Request = None
):
    """
    Legacy endpoint for handling OAuth callback (kept for backward compatibility).

    This endpoint processes the authorization code from Microsoft OAuth
    and creates a new Outlook connection associated with the admin.

    Args:
        code: Authorization code from Microsoft
        state: State parameter for verification

    Returns:
        Connection response with success status and connection details
    """
    # Use the same logic as the main callback endpoint
    return await handle_oauth_callback(code, state, outlook_service, request)



@router.get("/connections/by-email/{user_email}")
async def get_connection_by_email(
    user_email: str,
    current_admin: Dict[str, Any] = Depends(get_current_admin),
    outlook_service: AdminOutlookService = Depends(get_admin_outlook_service)
):
    """
    Find Outlook connection by user email address (Admin Only).

    This endpoint helps admins find the correct connection ID for an email address.
    Use this to get the connection_id needed for other API calls.

    Args:
        user_email: The email address to search for
        current_admin: Current authenticated admin

    Returns:
        Connection information if found, or error if not found

    Raises:
        HTTPException: If connection not found or service error
    """
    try:
        admin_email = current_admin["admin_email"]
        logger.info(f"Admin {admin_email} looking up connection for email: {user_email}")

        connection = await outlook_service.get_connection_by_email(admin_email, user_email)

        if not connection:
            # Use jsonable_encoder to ensure all fields are serializable
            return jsonable_encoder({
                    "success": False,
                "error_code": "NOT_FOUND",
                "error_message": f"No Outlook connection found for email: {user_email}. Please connect this email account first using the /auth/url endpoint.",
                "timestamp": datetime.utcnow()
            })

        # Try to start email monitoring for this connection
        try:
            email_monitor = get_email_monitor_service(outlook_service)
            
            # Force start monitoring even if it's already running
            if email_monitor.is_running:
                logger.info(f"Email monitoring is already running, stopping it first to restart")
                await email_monitor.stop_monitoring()
            
            # Start monitoring only for this specific connection
            connection_id = str(connection.id)
            await email_monitor.start_monitoring([connection_id])
            logger.info(f"✅ Email monitoring started/restarted for specific connection: {connection_id}")
        except Exception as e:
            # Log but don't fail the request if monitoring fails to start
            logger.error(f"Failed to start email monitoring automatically: {str(e)}")

        # Also encode the connection object
        return jsonable_encoder({
            "success": True,
            "connection": {
                "id": str(connection.id),
                "user_email": connection.user_email,
                "is_active": connection.is_active,
                "created_at": connection.created_at,
                "last_sync_at": connection.last_sync_at
            }
        })

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error looking up connection by email: {str(e)}")
        return jsonable_encoder({
                "success": False,
            "error_code": "INTERNAL_SERVER_ERROR",
            "error_message": "An internal server error occurred",
            "timestamp": datetime.utcnow()
        })


@router.post("/monitor/start")
async def start_email_monitoring(
    request: Request,
    connection_ids: Optional[str] = Query(None, description="Comma-separated list of connection IDs to monitor (leave empty to monitor all)"),
    current_admin: Dict[str, Any] = Depends(get_current_admin),
    outlook_service: AdminOutlookService = Depends(get_admin_outlook_service)
):
    """
    Start the background email monitoring service (Admin Only).

    This service will continuously monitor Outlook connections for new emails.
    You can specify specific connection IDs or leave empty to monitor all active connections.

    Args:
        connection_ids: Optional comma-separated list of connection IDs to monitor
        current_admin: Current authenticated admin
    """
    try:
        admin_email = current_admin["admin_email"]

        # Parse connection IDs if provided
        parsed_connection_ids = None
        if connection_ids:
            parsed_connection_ids = [id.strip() for id in connection_ids.split(",") if id.strip()]
            logger.info(f"Admin {admin_email} starting email monitoring service for specific connections: {parsed_connection_ids}")
        else:
            logger.info(f"Admin {admin_email} starting email monitoring service for all active connections")

        monitor_service = get_email_monitor_service(outlook_service)
        await monitor_service.start_monitoring(parsed_connection_ids)

        return {
            "success": True,
            "message": f"Email monitoring service started successfully for {'specific connections' if parsed_connection_ids else 'all active connections'}",
            "status": await monitor_service.get_monitoring_status()
        }

    except Exception as e:
        logger.error(f"Error starting email monitoring: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "success": False,
                "error": "monitor_start_error",
                "message": f"Failed to start email monitoring: {str(e)}"
            }
        )


@router.post("/monitor/stop")
async def stop_email_monitoring(
    request: Request,
    current_admin: Dict[str, Any] = Depends(get_current_admin),
    outlook_service: AdminOutlookService = Depends(get_admin_outlook_service)
):
    """
    Stop the background email monitoring service (Admin Only).
    """
    try:
        admin_email = current_admin["admin_email"]
        logger.info(f"Admin {admin_email} stopping email monitoring service via API")

        monitor_service = get_email_monitor_service(outlook_service)
        await monitor_service.stop_monitoring()

        return {
            "success": True,
            "message": "Email monitoring service stopped successfully",
            "status": await monitor_service.get_monitoring_status()
        }

    except Exception as e:
        logger.error(f"Error stopping email monitoring: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "success": False,
                "error": "monitor_stop_error",
                "message": f"Failed to stop email monitoring: {str(e)}"
            }
        )


@router.get("/monitor/status")
async def get_monitoring_status(
    request: Request,
    current_admin: Dict[str, Any] = Depends(get_current_admin),
    outlook_service: AdminOutlookService = Depends(get_admin_outlook_service)
):
    """
    Get the current status of the email monitoring service (Admin Only).

    This endpoint returns information about the monitoring service,
    including whether it's running, which connections are being monitored,
    and configuration details.
    """
    try:
        admin_email = current_admin["admin_email"]
        logger.info(f"Admin {admin_email} getting email monitoring status")

        monitor_service = get_email_monitor_service(outlook_service)
        status = await monitor_service.get_monitoring_status()

        return {
            "success": True,
            "message": "Monitoring status retrieved successfully",
            "status": status
        }

    except Exception as e:
        logger.error(f"Error getting monitoring status: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "success": False,
                "error": "monitor_status_error",
                "message": f"Failed to get monitoring status: {str(e)}"
            }
        )


@router.get("/connections")
async def list_connections(
    skip: int = Query(0, ge=0, description="Number of connections to skip"),
    limit: int = Query(50, ge=1, le=100, description="Number of connections to return"),
    active_only: bool = Query(True, description="Only return active connections"),
    current_admin: Dict[str, Any] = Depends(get_current_admin),
    outlook_service: AdminOutlookService = Depends(get_admin_outlook_service)
):
    """
    List all Outlook connections for the current admin (Admin Only).

    This endpoint returns a paginated list of Outlook connections
    associated with the current admin.

    Args:
        skip: Number of connections to skip (for pagination)
        limit: Maximum number of connections to return
        active_only: Only return active connections
        current_admin: Current authenticated admin

    Returns:
        List of connections with metadata

    Raises:
        HTTPException: For service errors
    """
    try:
        admin_email = current_admin["admin_email"]
        logger.info(f"Admin {admin_email} listing connections - skip: {skip}, limit: {limit}, active_only: {active_only}")

        result = await outlook_service.list_connections(admin_email, skip, limit, active_only)

        if result["success"]:
            logger.info(f"Retrieved {len(result['connections'])} connections for admin {admin_email}")
            return result
        else:
            logger.error(f"Failed to list connections: {result.get('message')}")
            raise HTTPException(
                status_code=500,
                detail=result
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error listing connections: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "success": False,
                "error": "list_connections_failed",
                "message": f"Failed to list connections: {str(e)}"
            }
        )


@router.get("/connections/detailed", response_model=OutlookConnectionDetailResponse)
async def get_detailed_connections(
    skip: int = Query(0, ge=0, description="Number of connections to skip"),
    limit: int = Query(50, ge=1, le=100, description="Number of connections to return"),
    active_only: bool = Query(True, description="Only return active connections"),
    current_admin: Dict[str, Any] = Depends(get_current_admin),
    outlook_service: AdminOutlookService = Depends(get_admin_outlook_service)
):
    """
    Get detailed information for all Outlook connections (Admin Only).

    This endpoint returns comprehensive connection details including:
    - Connection ID and user email address
    - Connection status (Connected/Disconnected/Active)
    - Creation date and last sync timestamp
    - Email count for each connection
    - Sync interval configuration
    - Token expiration and health indicators
    - Error messages if any

    This data is suitable for displaying connection cards/tiles in the UI
    with all necessary information for connection management.

    Args:
        skip: Number of connections to skip (for pagination)
        limit: Maximum number of connections to return
        active_only: Only return active connections
        current_admin: Current authenticated admin

    Returns:
        Detailed connection information with email counts and health status

    Raises:
        HTTPException: For service errors
    """
    try:
        admin_email = current_admin["admin_email"]
        logger.info(f"Admin {admin_email} requesting detailed connections - skip: {skip}, limit: {limit}, active_only: {active_only}")

        result = await outlook_service.get_detailed_connections(admin_email, skip, limit, active_only)

        if result["success"]:
            logger.info(f"Retrieved detailed information for {len(result['connections'])} connections for admin {admin_email}")
            return OutlookConnectionDetailResponse(**result)
        else:
            logger.error(f"Failed to get detailed connections: {result.get('message')}")
            raise HTTPException(
                status_code=500,
                detail=result
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting detailed connections: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "success": False,
                "error": "detailed_connections_failed",
                "message": f"Failed to get detailed connections: {str(e)}"
            }
        )


@router.get("/connections/{connection_id}/status", response_model=OutlookConnectionStatus)
async def get_connection_status(
    connection_id: str,
    current_admin: Dict[str, Any] = Depends(get_current_admin),
    outlook_service: AdminOutlookService = Depends(get_admin_outlook_service)
):
    """
    Get detailed status of a specific Outlook connection (Admin Only).

    This endpoint provides detailed information about a connection's status,
    including token validity, last sync time, and connection health.

    Args:
        connection_id: The ID of the connection to check
        current_admin: Current authenticated admin

    Returns:
        Detailed connection status information

    Raises:
        HTTPException: For invalid connection ID or service errors
    """
    try:
        admin_email = current_admin["admin_email"]
        logger.info(f"Admin {admin_email} getting status for connection: {connection_id}")

        status = await outlook_service.get_connection_status(admin_email, connection_id)
        
        if not status.is_connected and not status.is_token_valid:
            raise HTTPException(
                status_code=404,
                detail={
                    "success": False,
                    "error": "connection_not_found",
                    "message": f"Connection {connection_id} not found"
                }
            )

        logger.info(f"Connection {connection_id} status: connected={status.is_connected}, token_valid={status.is_token_valid}")
        return status
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting connection status: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "success": False,
                "error": "status_check_failed",
                "message": f"Failed to get connection status: {str(e)}"
            }
        )


@router.post("/connections/{connection_id}/disconnect")
async def disconnect_connection(
    connection_id: str,
    current_admin: Dict[str, Any] = Depends(get_current_admin),
    outlook_service: AdminOutlookService = Depends(get_admin_outlook_service)
):
    """
    Disconnect an Outlook connection.
    
    This endpoint deactivates an Outlook connection without deleting it.
    The connection can be reactivated later if needed.
    
    Args:
        connection_id: The ID of the connection to disconnect
        
    Returns:
        Disconnect result
        
    Raises:
        HTTPException: For invalid connection or service errors
    """
    try:
        logger.info(f"Disconnecting connection: {connection_id}")
        
        result = await outlook_service.disconnect_connection(connection_id)
        
        if result["success"]:
            logger.info(f"Successfully disconnected connection: {connection_id}")
            return result
        else:
            logger.error(f"Failed to disconnect connection: {result['message']}")
            raise HTTPException(
                status_code=400,
                detail=result
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error disconnecting connection: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "success": False,
                "error": "disconnect_error",
                "message": f"Failed to disconnect connection: {str(e)}"
            }
        )


@router.delete("/connections/{connection_id}")
async def delete_connection(
    connection_id: str,
    current_admin: Dict[str, Any] = Depends(get_current_admin),
    outlook_service: AdminOutlookService = Depends(get_admin_outlook_service)
):
    """
    Delete an Outlook connection and all associated data (Admin Only).

    This endpoint permanently deletes an Outlook connection and all
    associated email tracking data. This action cannot be undone.

    Args:
        connection_id: The ID of the connection to delete
        current_admin: Current authenticated admin

    Returns:
        Delete result

    Raises:
        HTTPException: For invalid connection or service errors
    """
    try:
        admin_email = current_admin["admin_email"]
        logger.info(f"Admin {admin_email} deleting connection: {connection_id}")

        result = await outlook_service.delete_connection(admin_email, connection_id)

        if result["success"]:
            logger.info(f"Successfully deleted connection: {connection_id} for admin {admin_email}")
            return result
        else:
            logger.error(f"Failed to delete connection: {result['message']}")
            raise HTTPException(
                status_code=404 if "not found" in result["message"].lower() else 500,
                detail=result
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting connection: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "success": False,
                "error": "delete_error",
                "message": f"Failed to delete connection: {str(e)}"
            }
        )


@router.post("/monitor/configure")
async def configure_monitoring(
    request: Request,
    sync_interval: int = Query(60, ge=30, le=3600, description="Sync interval in seconds (30-3600)"),
    current_admin: Dict[str, Any] = Depends(get_current_admin),
    outlook_service: AdminOutlookService = Depends(get_admin_outlook_service)
):
    """
    Configure the email monitoring service settings.

    Args:
        sync_interval: How often to check for new emails (in seconds, min 30, max 3600)
    """
    try:
        logger.info(f"Configuring email monitoring - sync_interval: {sync_interval}s")

        monitor_service = get_email_monitor_service(outlook_service)
        monitor_service.set_sync_interval(sync_interval)

        return {
            "success": True,
            "message": f"Email monitoring configured successfully",
            "status": await monitor_service.get_monitoring_status()
        }

    except Exception as e:
        logger.error(f"Error configuring email monitoring: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "success": False,
                "error": "monitor_config_error",
                "message": f"Failed to configure email monitoring: {str(e)}"
            }
        )


@router.post("/monitor/connections/add")
async def add_connection_to_monitoring(
    connection_id: str = Query(..., description="Connection ID to add to monitoring"),
    current_admin: Dict[str, Any] = Depends(get_current_admin),
    outlook_service: AdminOutlookService = Depends(get_admin_outlook_service)
):
    """
    Add a specific connection to the monitoring service.

    This endpoint allows you to add a connection to the monitoring list
    without restarting the entire monitoring service.

    Args:
        connection_id: The connection ID to add to monitoring
    """
    try:
        logger.info(f"Adding connection {connection_id} to monitoring")

        monitor_service = get_email_monitor_service(outlook_service)
        monitor_service.add_monitored_connection(connection_id)

        return {
            "success": True,
            "message": f"Connection {connection_id} added to monitoring successfully",
            "status": await monitor_service.get_monitoring_status()
        }

    except Exception as e:
        logger.error(f"Error adding connection to monitoring: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "success": False,
                "error": "monitor_add_connection_error",
                "message": f"Failed to add connection to monitoring: {str(e)}"
            }
        )


@router.post("/monitor/connections/remove")
async def remove_connection_from_monitoring(
    connection_id: str = Query(..., description="Connection ID to remove from monitoring"),
    current_admin: Dict[str, Any] = Depends(get_current_admin),
    outlook_service: AdminOutlookService = Depends(get_admin_outlook_service)
):
    """
    Remove a specific connection from the monitoring service.

    This endpoint allows you to remove a connection from the monitoring list
    without stopping the entire monitoring service.

    Args:
        connection_id: The connection ID to remove from monitoring
    """
    try:
        logger.info(f"Removing connection {connection_id} from monitoring")

        monitor_service = get_email_monitor_service(outlook_service)
        monitor_service.remove_monitored_connection(connection_id)

        return {
            "success": True,
            "message": f"Connection {connection_id} removed from monitoring successfully",
            "status": await monitor_service.get_monitoring_status()
        }

    except Exception as e:
        logger.error(f"Error removing connection from monitoring: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "success": False,
                "error": "monitor_remove_connection_error",
                "message": f"Failed to remove connection from monitoring: {str(e)}"
            }
        )


@router.post("/monitor/connections/set")
async def set_monitored_connections(
    connection_ids: Optional[str] = Query(None, description="Comma-separated list of connection IDs to monitor (leave empty to monitor all)"),
    current_admin: Dict[str, Any] = Depends(get_current_admin),
    outlook_service: AdminOutlookService = Depends(get_admin_outlook_service)
):
    """
    Set the specific connections to monitor.

    This endpoint allows you to update the entire list of monitored connections
    without restarting the monitoring service.

    Args:
        connection_ids: Comma-separated list of connection IDs to monitor, or empty to monitor all
    """
    try:
        # Parse connection IDs if provided
        parsed_connection_ids = None
        if connection_ids:
            parsed_connection_ids = [id.strip() for id in connection_ids.split(",") if id.strip()]
            logger.info(f"Setting monitored connections to: {parsed_connection_ids}")
        else:
            logger.info("Setting monitoring to include all active connections")

        monitor_service = get_email_monitor_service(outlook_service)
        monitor_service.set_monitored_connections(parsed_connection_ids)

        return {
            "success": True,
            "message": f"Monitored connections updated successfully to {'specific connections' if parsed_connection_ids else 'all active connections'}",
            "status": await monitor_service.get_monitoring_status()
        }

    except Exception as e:
        logger.error(f"Error setting monitored connections: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "success": False,
                "error": "monitor_set_connections_error",
                "message": f"Failed to set monitored connections: {str(e)}"
            }
        )