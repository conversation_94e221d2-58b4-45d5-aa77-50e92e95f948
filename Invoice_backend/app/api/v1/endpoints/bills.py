"""
Bill management endpoints
"""

import logging
from typing import Optional, Dict, Any
from fastapi import APIRouter, HTTPException, Depends, Query
from motor.motor_asyncio import AsyncIOMotorDatabase

from app.core.database import get_db
from app.core.auth import get_current_admin
from app.schemas.bill import <PERSON><PERSON><PERSON>, BillResponse, BillListResponse, BillCreateResponse
from app.services.bill_service import BillService
from app.services.quickbooks_database import QuickBooksDatabase

logger = logging.getLogger(__name__)
router = APIRouter()


def get_quickbooks_database(db: AsyncIOMotorDatabase = Depends(get_db)) -> QuickBooksDatabase:
    """Get QuickBooks database service"""
    return QuickBooksDatabase(db)


async def get_active_connection_id(qb_db: QuickBooksDatabase = Depends(get_quickbooks_database)) -> str:
    """Get the first active QuickBooks connection ID"""
    connections_result = await qb_db.list_connections(skip=0, limit=100, active_only=True)
    if not connections_result["success"] or not connections_result["connections"]:
        raise HTTPException(
            status_code=404,
            detail={
                "success": False,
                "error": "No connections found",
                "details": "No QuickBooks connections available. Please create a connection first."
            }
        )

    # Use the first active connection
    connections = connections_result["connections"]
    active_connection = None
    for conn in connections:
        if conn.get("is_active", True):  # Default to True if not specified
            active_connection = conn
            break

    if not active_connection:
        raise HTTPException(
            status_code=404,
            detail={
                "success": False,
                "error": "No active connections found",
                "details": "No active QuickBooks connections available. Please activate a connection first."
            }
        )

    return active_connection["id"]


@router.post("/", response_model=BillCreateResponse)
async def create_bill(
    bill_data: BillCreate,
    current_admin: Dict[str, Any] = Depends(get_current_admin),
    db: AsyncIOMotorDatabase = Depends(get_db),
    connection_id: str = Depends(get_active_connection_id)
):
    """
    Create a new bill in QuickBooks and store in database.
    
    This endpoint accepts structured bill data and:
    1. Saves the bill to MongoDB
    2. Creates a corresponding bill in QuickBooks
    3. Updates the database record with QuickBooks information
    
    Args:
        bill_data: BillCreate model containing vendor info, amounts, dates, and line items
        
    Returns:
        JSON response with success status and bill details or error information
        
    Raises:
        HTTPException: For validation errors, authentication failures, or API errors
    """
    try:
        bill_service = BillService(db, connection_id)
        
        # Log the incoming request
        logger.info(f"Received bill creation request for vendor: {bill_data.vendor.name}")
        logger.info(f"Bill amount: ${bill_data.total_amount:.2f}")
        
        # Create bill
        result = await bill_service.create_bill(bill_data)
        
        if result.success:
            logger.info(f"Bill created successfully: {result.bill_id}")
            return result
        else:
            logger.error(f"Bill creation failed: {result.message}")
            raise HTTPException(
                status_code=400,
                detail={
                    "success": False,
                    "error": "Bill creation failed",
                    "details": result.message
                }
            )
            
    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
        
    except Exception as e:
        # Handle unexpected errors
        error_msg = f"Unexpected error processing bill creation: {str(e)}"
        logger.error(error_msg)
        raise HTTPException(
            status_code=500,
            detail={
                "success": False,
                "error": "Internal server error",
                "details": error_msg
            }
        )

@router.get("/", response_model=BillListResponse)
async def get_bills(
    skip: int = Query(0, ge=0, description="Number of bills to skip"),
    limit: int = Query(10, ge=1, le=100, description="Number of bills to return"),
    vendor_name: Optional[str] = Query(None, description="Filter by vendor name"),
    status: Optional[str] = Query(None, description="Filter by status"),
    current_admin: Dict[str, Any] = Depends(get_current_admin),
    db: AsyncIOMotorDatabase = Depends(get_db),
    connection_id: str = Depends(get_active_connection_id)
):
    """
    Get paginated list of bills with optional filters.
    
    Args:
        skip: Number of bills to skip (for pagination)
        limit: Number of bills to return (max 100)
        vendor_name: Optional filter by vendor name
        status: Optional filter by status (pending, created, failed)
        
    Returns:
        Paginated list of bills with metadata
    """
    try:
        bill_service = BillService(db, connection_id)
        result = await bill_service.get_bills(
            skip=skip,
            limit=limit,
            vendor_name=vendor_name,
            status=status
        )
        
        return BillListResponse(**result)
        
    except Exception as e:
        error_msg = f"Error retrieving bills: {str(e)}"
        logger.error(error_msg)
        raise HTTPException(
            status_code=500,
            detail={
                "success": False,
                "error": "Internal server error",
                "details": error_msg
            }
        )

@router.get("/{bill_id}", response_model=BillResponse)
async def get_bill(
    bill_id: str,
    current_admin: Dict[str, Any] = Depends(get_current_admin),
    db: AsyncIOMotorDatabase = Depends(get_db),
    connection_id: str = Depends(get_active_connection_id)
):
    """
    Get a specific bill by ID.
    
    Args:
        bill_id: The ID of the bill to retrieve
        
    Returns:
        Bill details
        
    Raises:
        HTTPException: If bill not found
    """
    try:
        bill_service = BillService(db, connection_id)
        bill = await bill_service.get_bill_by_id(bill_id)
        
        if not bill:
            raise HTTPException(
                status_code=404,
                detail={
                    "success": False,
                    "error": "Bill not found",
                    "details": f"No bill found with ID: {bill_id}"
                }
            )
        
        return bill
        
    except HTTPException:
        raise
        
    except Exception as e:
        error_msg = f"Error retrieving bill {bill_id}: {str(e)}"
        logger.error(error_msg)
        raise HTTPException(
            status_code=500,
            detail={
                "success": False,
                "error": "Internal server error",
                "details": error_msg
            }
        )

@router.delete("/{bill_id}")
async def delete_bill(
    bill_id: str,
    db: AsyncIOMotorDatabase = Depends(get_db)
):
    """
    Delete a bill by ID.
    
    Args:
        bill_id: The ID of the bill to delete
        
    Returns:
        Success confirmation
        
    Raises:
        HTTPException: If bill not found or deletion fails
    """
    try:
        bill_service = BillService(db)
        
        # Check if bill exists
        bill = await bill_service.get_bill_by_id(bill_id)
        if not bill:
            raise HTTPException(
                status_code=404,
                detail={
                    "success": False,
                    "error": "Bill not found",
                    "details": f"No bill found with ID: {bill_id}"
                }
            )
        
        # Delete the bill
        success = await bill_service.delete_bill(bill_id)
        
        if success:
            return {
                "success": True,
                "message": f"Bill {bill_id} deleted successfully"
            }
        else:
            raise HTTPException(
                status_code=500,
                detail={
                    "success": False,
                    "error": "Deletion failed",
                    "details": f"Failed to delete bill {bill_id}"
                }
            )
        
    except HTTPException:
        raise
        
    except Exception as e:
        error_msg = f"Error deleting bill {bill_id}: {str(e)}"
        logger.error(error_msg)
        raise HTTPException(
            status_code=500,
            detail={
                "success": False,
                "error": "Internal server error",
                "details": error_msg
            }
        )